{"loyalty": {"points": {"balance": "Puntos disponibles", "earn": "<PERSON><PERSON> {{points}} puntos", "spend": "<PERSON><PERSON><PERSON>"}, "referral": {"invite": "Invitar a un amigo", "reward": "<PERSON>ane {{points}} puntos por cada referido"}, "vip": {"status": "Estado VIP", "progress": "Progreso hacia estado VIP"}}, "common": {"save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "loading": "Cargando...", "error": "Se ha producido un error", "search": "Buscar...", "logout": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "add": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "view": "<PERSON>er", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "Sí", "no": "No", "active": "Activo", "inactive": "Inactivo", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": "Éxito", "warning": "Advertencia", "info": "Información", "name": "Nombre", "description": "Descripción", "status": "Estado", "actions": "Acciones", "settings": "Configuración", "configuration": "Configuración"}, "admin": {"navigation": {"dashboard": "Panel de control", "program": "Programa", "customers": "Clientes", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Configuración", "promotions": "Promociones", "history": "Historial", "pointsShop": "Tienda de puntos", "overview": "Resumen", "pointsConfig": "Configuración de puntos", "referrals": "Referencias", "vipProgram": "Programa VIP", "bonusCampaigns": "Campañas de bonificación", "generalSettings": "Configuración general", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "Productos canjeables"}, "dashboard": {"title": "Panel de control", "totalPoints": "Puntos totales", "activeMembers": "Miembros activos", "redemptionRate": "<PERSON><PERSON>", "pointsEvolution": "Evolución de puntos (30 días)", "pointsDistribution": "Distribución de puntos por tipo", "recentActivity": "Actividad reciente", "loadingChart": "Cargando gráfico...", "tableHeaders": {"customer": "Cliente", "action": "Acción", "points": "Punt<PERSON>", "date": "<PERSON><PERSON>"}, "averagePointsPerCustomer": "Puntos promedio / cliente", "dataAvailableSoon": "Los datos estarán disponibles pronto...", "pointsInCirculation": "Puntos en circulación", "rewardsThisMonth": "Recompensas este mes"}, "program": {"title": "Programa de fidelidad", "overview": "Resumen", "status": {"active": "Programa activo", "inactive": "Programa inactivo", "activate": "Activar", "deactivate": "Desactivar", "activeDescription": "Su programa de fidelidad está actualmente activo y sus clientes pueden ganar puntos.", "inactiveDescription": "Su programa está actualmente inactivo. Actívelo para permitir que sus clientes ganen puntos."}, "generalConfiguration": "Configuración general", "programName": "Nombre del programa", "programDescription": "Descripción del programa", "quickActions": "Acciones rápidas", "pointsConfiguration": "Configuración de puntos", "referralProgram": "Programa de referencias", "stats": {"title": "Estadísticas del programa", "totalCustomers": "Clientes totales", "activeCustomers": "Clientes activos", "totalPointsEarned": "Puntos ganados totales", "totalPointsRedeemed": "Puntos canjeados totales", "totalRewards": "Recompensas totales", "pendingReferrals": "Referencias pendientes", "completedReferrals": "Referencias completadas", "pointsDistributed": "Puntos distribuidos"}, "paramSaveSuccess": "Parámetros guardados exitosamente", "paramSaveError": "Error al guardar los parámetros", "saveModifications": "Guardar modificaciones", "saveDescription": "Tiene modificaciones sin guardar"}, "customers": {"title": "Clientes", "member": "Miembro", "guest": "<PERSON><PERSON><PERSON><PERSON>", "points": "puntos", "referrals": "persona(s)", "email": "Email", "joinedOn": "Se unió el", "type": "Tipo", "filters": {"type": "Tipo", "search": "Buscar por nombre o email"}, "pagination": "Página {{current}} de {{total}}", "back": "Atrás", "viewInShopify": "Ver en Shopify", "infoTitle": "Información del cliente", "activityTitle": "Actividad", "pointsTab": "Punt<PERSON>", "referralsTab": "Referencias", "rewardsTab": "Recompensas", "ordersTitle": "Pedidos", "orderId": "ID de pedido", "total": "Total", "status": "Estado", "date": "<PERSON><PERSON>", "noOrders": "Sin pedidos", "currentBalance": "<PERSON><PERSON>", "statsTitle": "Estadísticas", "totalSpent": "Total gastado", "ordersCount": "Pedidos", "completedReferrals": "Referencias", "referralTitle": "Referencia", "referralFeatureComing": "Funcionalidad en desarrollo", "referralCodeInfo": "Código de referencia y enlace se mostrarán aquí", "action": "Acción", "referee": "Referido", "referralStatus": "Estado", "referralOrderTotal": "Total del pedido", "reward": "Recompensa", "code": "Código", "noRewards": "Ninguna recompensa canjeada", "none": "—", "earned": "Ganado", "redeemed": "<PERSON><PERSON><PERSON>", "signup": "Registro", "validated": "Validado", "pending": "Pendiente", "paid": "<PERSON><PERSON>", "anonymous": "Cliente anónimo", "client": "Cliente", "errors": {"customerIdRequired": "ID de cliente requerido", "customerNotFound": "Cliente no encontrado", "actionNotRecognized": "Acción no reconocida"}, "success": {"referralLinkGenerated": "Enlace de referido generado exitosamente"}, "referralInterface": {"currentReferralLink": "<PERSON>lace de referido actual", "code": "Código", "expiresOn": "Expira el", "copied": "¡Copiado!", "copyLink": "<PERSON><PERSON><PERSON> enlace", "noActiveReferralLink": "No hay enlace de referido activo", "generateReferralLink": "Generar enlace de referido", "referralTableHeaders": {"referee": "Referido", "code": "Código", "status": "Estado", "validationDate": "Fecha de validación", "expirationDate": "Fecha de expiración", "pointsEarned": "Puntos ganados"}, "referralStatuses": {"pending": "Pendiente", "completed": "Completado", "expired": "<PERSON><PERSON><PERSON>"}, "referralStats": {"title": "Estadísticas de Referidos", "referralsSent": "Referidos enviados", "referralsValidated": "Referidos validados", "pending": "Pendiente", "pointsEarned": "Puntos ganados", "currentLink": "Enlace actual", "activeUntil": "Activo hasta", "referredBy": "Referido por"}}}, "analytics": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Análisis del programa de fidelidad", "memberStats": "Estadísticas de miembros", "totalMembers": "Miembros totales", "newMembersLast30Days": "Nuevos miembros (30 días)", "pointsTransactions": "Transacciones de puntos", "totalTransactions": "Transacciones totales", "pointsDistributed": "Puntos distribuidos", "referralPurchases": "Compras por referencia", "referralRevenue": "Ingresos por referencia", "trends": "Tendencias", "membersGrowth": "Crecimiento de miembros", "pointsGrowth": "Crecimiento de puntos", "revenueGrowth": "Crecimiento de ingresos"}, "settings": {"title": "Configuración", "quickNavigation": "Navegación rápida", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "Productos canjeables", "generalSettings": "Configuración general", "shopName": "Nombre de la tienda", "currency": "Moneda", "language": "Idioma", "emailNotifications": "Notificaciones por email", "pointsName": "Nombre de los puntos", "welcomeMessage": "Mensaje de bienvenida", "saveSuccess": "Configuración guardada exitosamente", "saveError": "Error al guardar la configuración", "customizationTitle": "Personalización", "pointsNameHelp": "Este nombre se usará en toda la aplicación", "welcomeMessageHelp": "Mensaje mostrado a los nuevos clientes", "notificationsTitle": "Notificaciones", "senderEmail": "Correo del remitente", "saveCardTitle": "💾 Guardar los cambios", "saveButton": "Guardar los cambios", "noChanges": "Sin cambios", "unsavedChanges": "Tienes cambios sin guardar", "senderEmailHelp": "Dejar vacío para usar configuración por defecto"}, "emails": {"title": "Configuración de Correos", "subtitle": "Gestionar notificaciones por correo del programa de fidelidad", "backToSettings": "Configuración", "configurationStatus": "Estado de Configuración", "emailService": "<PERSON><PERSON><PERSON>", "configured": "<PERSON><PERSON><PERSON><PERSON>", "notConfigured": "No configurado", "notificationsEnabled": "Notificaciones activadas", "enabled": "<PERSON><PERSON>das", "disabled": "Desactivadas", "generalSettings": "Configuración General", "enableNotifications": "Activar notificaciones por correo", "enableNotificationsHelp": "Los clientes recibirán correos para eventos del programa de fidelidad", "shopName": "Nombre de la tienda", "shopNameHelp": "Nombre mostrado en los correos", "pointsName": "Nombre de los puntos", "pointsNameHelp": "Nombre usado para puntos en correos (ej: BROpoints)", "automaticEmailTypes": "Tipos de Correos Automáticos", "welcomeEmail": "Correo de bienvenida", "welcomeEmailDesc": "Enviado al unirse al programa", "pointsEarned": "Puntos ganados", "pointsEarnedDesc": "Enviado cuando el cliente gana puntos", "pointsUsed": "Puntos usados", "pointsUsedDesc": "Enviado cuando el cliente usa puntos", "levelPromotion": "Promoción de nivel", "levelPromotionDesc": "Enviado cuando cambia el nivel VIP", "referralReward": "Recompensa de referido", "referralRewardDesc": "Enviado cuando el referido es exitoso", "active": "Activo", "actions": "Acciones", "saveSettings": "Guardar configuración", "sendTestEmail": "Enviar correo de <PERSON>", "testEmailRequirement": "El correo de prueba requiere que el servicio esté configurado y activado", "configuredProvider": "Prove<PERSON><PERSON> configurado"}, "points": {"title": "Configuración de puntos", "waysToEarn": "Formas de ganar puntos", "waysToRedeem": "Formas de canjear puntos", "addWayToEarn": "<PERSON><PERSON><PERSON> de gana<PERSON>", "addWayToRedeem": "<PERSON><PERSON><PERSON> de can<PERSON>", "earnDescription": "Configure las diferentes formas en que sus clientes pueden ganar puntos. Puede crear acciones con puntos por euro gastado (ej: 5 puntos/€1) o puntos fijos para acciones específicas (ej: 100 puntos por registro).", "redeemDescription": "Configure las recompensas que sus clientes pueden obtener a cambio de sus puntos.", "minPoints": "Desde {{points}} puntos", "exactPoints": "{{points}} puntos", "configurable": "Configurable", "fixed": "<PERSON><PERSON>", "baseSettings": "Configuración básica", "earningRateLabel": "Tasa de ganancia (puntos/€)", "redemptionRateLabel": "Tasa de conversión (puntos/€)", "redemptionRateHelp": "Número de puntos necesarios para 1€ de descuento (ej: 100 puntos = 1€)", "minimumPointsLabel": "Puntos mínimos para canjear", "expirationDaysLabel": "Caducidad de puntos (días)", "referralPointsLabel": "Puntos por referido", "birthdayPointsLabel": "Puntos de cumpleaños", "save": "Guardar", "previewTitle": "Previsualización", "previewAmountLabel": "Importe de compra (€)", "previewForAmount": "Por una compra de {amount}€:", "previewPoints": "Puntos ganados: {points} puntos", "previewValue": "Valor en €: {value}€", "waysToEarnTitle": "Formas de ganar puntos", "waysToEarnDescription": "Configura las diferentes formas en que tus clientes pueden ganar puntos. Puedes crear acciones con puntos por euro gastado (ej: 5 puntos/1€) o puntos fijos para acciones específicas (ej: 100 puntos por registro).", "fixedPoints": "{points} puntos fijos", "pointsPerEuro": "{points} puntos por 1€ gastado", "active": "Activo", "inactive": "Inactivo", "edit": "<PERSON><PERSON>", "seeAllWaysToEarn": "Ver todas las formas de ganar", "seeAllWaysToEarnCount": "Ver todas las formas de ganar ({count})", "waysToRedeemTitle": "Formas de canjear puntos", "waysToRedeemDescription": "Configura las diferentes recompensas que tus clientes pueden obtener canjeando sus puntos. Por ejemplo, descuentos en sus pedidos, productos gratis o envío gratuito.", "fromPoints": "<PERSON><PERSON> {points} puntos", "pointsCost": "{points} puntos", "seeAllWaysToRedeem": "Ver todas las formas de canjear", "seeAllWaysToRedeemCount": "Ver todas las formas de canjear ({count})", "successUpdate": "Configuración actualizada con éxito", "errorUpdate": "Error al actualizar la configuración", "errorAllFieldsRequired": "Todos los campos son obligatorios", "errorAllNumbers": "Todos los valores deben ser números válidos", "errorPositiveValues": "Los valores deben ser positivos"}, "referrals": {"title": "Programa de referidos", "description": "El programa de referidos permite a tus clientes leales recomendar tu tienda a sus amigos y familiares. Cuando un cliente refiere a un amigo que realiza su primera compra, ambas partes reciben recompensas. Es una excelente manera de adquirir nuevos clientes y recompensar la lealtad de los existentes.", "howItWorks": "Cómo funciona:", "step1": "El padrino comparte su código de referido único con sus amigos", "step2": "El ahijado usa este código en su primer pedido", "step3": "Si el pedido alcanza el monto mínimo, se distribuyen las recompensas", "step4": "<PERSON><PERSON>, padrino y ahijado, reciben sus recompensas respectivas", "programStatus": "Estado del programa", "active": "Activo", "inactive": "Inactivo", "activate": "Activar", "deactivate": "Desactivar", "activeDescription": "Tus clientes pueden actualmente referir amigos y ganar recompensas.", "inactiveDescription": "El programa de referidos está actualmente desactivado. Actívalo para permitir que tus clientes refieran amigos.", "referrerRewardTitle": "Recompensa del padrino", "referrerGets": "El padrino recibe {reward}", "referredRewardTitle": "Recompensa del ahijado", "referredGets": "El ahijado recibe {reward}", "rewardTypeLabel": "Tipo de recompensa", "rewardTypePoints": "Punt<PERSON>", "rewardTypeFixed": "Descuento fijo (€)", "rewardTypeDiscount": "Po<PERSON>entaje (%)", "rewardAmountPoints": "Cantidad en puntos", "rewardAmountFixed": "Monto del descuento (€)", "rewardAmountDiscount": "Porcentaje de descuento (%)", "rewardAmountDefault": "Monto de la recompensa", "conditionsTitle": "Condiciones", "minPurchaseLabel": "Compra mínima para el ahijado (€)", "minPurchaseHelp": "<PERSON><PERSON> mínimo que el ahijado debe gastar para validar la referencia", "expiryDaysLabel": "Validez de la invitación (días)", "expiryDaysHelp": "Cantidad de días que la invitación permanece válida", "customizationTitle": "Personalización", "customMessageLabel": "Mensaje de invitación", "customMessageHelp": "Este mensaje se mostrará en la página de referidos", "save": "Guardar cambios", "referralLinksTitle": "Gestión de enlaces de referidos", "referralLinksDescription": "Genera y gestiona los enlaces de referidos para tus clientes.", "customerTableName": "Nombre", "customerTableEmail": "Email", "customerTableType": "Tipo", "customerTablePoints": "Punt<PERSON>", "customerTableStatus": "Estado de referido", "customerTableAction": "Acción", "member": "Miembro", "guest": "<PERSON><PERSON><PERSON><PERSON>", "linkActive": "Enlace activo", "noLink": "Sin enlace", "generateLink": "<PERSON><PERSON> enlace", "existingLink": "Enlace existente", "noCustomers": "No se encontraron clientes. Asegúrate de tener clientes en tu tienda.", "linkFormatTitle": "Formato de los enlaces de referido", "linkFormatDescription": "Los enlaces generados siguen este formato:", "linkFormatExample": "https://tu-tienda.myshopify.com?ref=eyxxxxxxxxxxxxxxxx", "linkFormatHelp": "<PERSON><PERSON> \"eyxxxxxxxxxxxxxxxx\" es un token único seguro en base64.", "linkHowItWorks": "Cómo funciona:", "linkStep1": "El cliente genera su enlace a través del widget de fidelidad", "linkStep2": "Comparte el enlace en redes sociales o por email", "linkStep3": "Un amigo hace clic en el enlace y es redirigido a tu tienda", "linkStep4": "El código se captura y almacena automáticamente", "linkStep5": "En la compra, la referencia se valida y se distribuyen las recompensas", "statsTitle": "Estadísticas de referidos", "statsTotal": "Total referidos", "statsCompleted": "Completados", "statsPending": "Pendientes", "statsConversion": "Tasa de conversión", "helpTitle": "<PERSON><PERSON><PERSON>", "helpDescription1": "El programa de referidos permite a tus clientes recomendar tu tienda a sus amigos. Padrinos y ahijados reciben recompensas cuando la referencia es validada.", "helpDescription2": "Una referencia se valida cuando el ahijado realiza su primera compra alcanzando el monto mínimo definido."}, "widget": {"title": "Personalización del widget", "appearance": "Apariencia", "colors": "Colores", "position": {"label": "Posición del widget", "bottomRight": "Abajo a la derecha", "bottomLeft": "Abajo a la izquierda", "topRight": "Arriba a la derecha", "topLeft": "Arriba a la izquierda"}, "size": {"label": "<PERSON><PERSON><PERSON> del widget", "small": "Pequeño", "medium": "Mediano", "large": "Grande"}, "borders": {"label": "<PERSON><PERSON>", "square": "Cuadrado", "rounded": "Redondeado", "pill": "Píldora"}, "shadow": "Sombra", "animation": "Animación", "showPointsOnButton": "Mostrar puntos en el botón", "primaryColor": "Color principal", "secondaryColor": "Color secundario", "textColor": "Color del texto", "preview": "Vista previa", "previewDescription": "Vista previa en tiempo real de su widget", "loyaltyProgram": "Programa de Fidelidad", "welcomeTo": "Bienvenido a", "welcomeMessage": "¡Bienvenido a nuestro programa de fidelidad!", "guest": "<PERSON><PERSON><PERSON><PERSON>", "member": "Miembro", "points": "Punt<PERSON>", "orders": "Pedidos", "nextReward": "Próxima recompensa", "pointsNeeded": "Faltan {{count}} puntos", "yourRewards": "<PERSON><PERSON> recompensas", "oneRewardAvailable": "Tienes 1 recompensa disponible", "waysToEarn": "Formas de ganar", "waysToRedeem": "Formas de canjear", "referFriends": "Recomienda a tus amigos", "referralsCompleted": "{{count}} refer<PERSON><PERSON> completados", "shareUrl": "Comparte esta URL para dar a tus amigos un cupón de 4€", "facebook": "Facebook", "x": "X", "email": "Email", "yourActivity": "Tu actividad", "poweredBy": "Powered by <PERSON><PERSON><PERSON>"}, "notifications": {"languageChanged": "Idioma cambiado a {{language}}"}, "exchangeableProducts": {"title": "Productos canjeables", "subtitle": "Gestiona los productos que tus clientes pueden obtener a cambio de puntos", "addProduct": "Agregar producto", "emptyStateHeading": "Ningún producto canjeable configurado", "emptyStateDescription": "Comienza agregando productos que tus clientes puedan canjear por puntos.", "product": "Producto", "image": "Imagen", "pointsCost": "Costo en puntos", "status": "Estado", "actions": "Acciones", "calculatedAuto": "Calculado automáticamente", "active": "Activo", "inactive": "Inactivo", "activate": "Activar", "deactivate": "Desactivar", "delete": "Eliminar", "successAdd": "Agregando {count} producto(s)...", "successDelete": "Producto canjeable eliminado con éxito", "successToggle": "Estado del producto {status} con éxito", "errorSelectOne": "Por favor selecciona al menos un producto", "modalTitle": "Agregar producto canjeable", "modalPrimary": "Agregar", "modalSecondary": "<PERSON><PERSON><PERSON>", "modalDescription": "Selecciona los productos que tus clientes podrán canjear por puntos. El costo en puntos se definirá en la configuración del programa.", "selectedCount": "{count} producto(s) seleccionado(s). El costo en puntos se configurará automáticamente según la configuración del programa."}, "productSelector": {"title": "Productos canjeables con puntos", "addProducts": "Agregar productos", "description": "Selecciona los productos que tus clientes pueden comprar con sus puntos de fidelidad.", "empty": "Ningún producto seleccionado. Haz clic en 'Agregar productos' para comenzar.", "price": "Precio", "remove": "Eliminar", "selectProducts": "Seleccionar productos", "close": "<PERSON><PERSON><PERSON>", "searchLabel": "Buscar productos", "searchPlaceholder": "Nombre del producto...", "loading": "Cargando productos...", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}