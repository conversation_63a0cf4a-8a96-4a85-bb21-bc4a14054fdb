{% comment %}
  Bloc Liquid : Widget de fidélité pour page produit
  Affiche les points à gagner et ouvre le hub au clic
{% endcomment %}

{% assign product_price = product.selected_or_first_available_variant.price | default: product.price %}
{% assign estimated_points = product_price | divided_by: 100 | round %}

<div class="loyalty-product-widget" 
     onclick="window.loyaltyWidget && window.loyaltyWidget.open()"
     data-product-id="{{ product.id }}"
     data-variant-id="{{ product.selected_or_first_available_variant.id }}">
  
  {% if customer %}
    <!-- Client connecté -->
    <div class="loyalty-product-content">
      <div class="loyalty-product-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
        </svg>
      </div>
      
      <div class="loyalty-product-info">
        <div class="loyalty-product-title">Gagnez des BROpoints</div>
        <div class="loyalty-product-points">
          <span class="points-amount" data-points="{{ estimated_points }}">{{ estimated_points }}</span>
          <span class="points-label">points avec cet achat</span>
        </div>
        {% if customer.tags contains 'vip' %}
          <div class="loyalty-product-vip">
            <span class="vip-badge">{{ customer.metafields.loyalty.vip_level | default: 'VIP' }}</span>
            <span class="vip-multiplier">Bonus appliqué</span>
          </div>
        {% endif %}
      </div>
      
      <div class="loyalty-product-arrow">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
    
    <div class="loyalty-product-hover">
      <span>Cliquez pour voir vos récompenses</span>
    </div>
    
  {% else %}
    <!-- Client non connecté -->
    <div class="loyalty-product-content">
      <div class="loyalty-product-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
        </svg>
      </div>
      
      <div class="loyalty-product-info">
        <div class="loyalty-product-title">Rejoignez BROpoints</div>
        <div class="loyalty-product-points">
          <span class="points-amount">{{ estimated_points }}</span>
          <span class="points-label">points à gagner</span>
        </div>
        <div class="loyalty-product-signup">
          <span>Inscription gratuite</span>
        </div>
      </div>
      
      <div class="loyalty-product-arrow">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
    
    <div class="loyalty-product-hover">
      <span>Cliquez pour découvrir le programme</span>
    </div>
  {% endif %}
</div>

<style>
  .loyalty-product-widget {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 16px;
    margin: 16px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .loyalty-product-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.15);
    border-color: #2E7D32;
  }
  
  .loyalty-product-widget:hover .loyalty-product-hover {
    opacity: 1;
    transform: translateY(0);
  }
  
  .loyalty-product-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .loyalty-product-icon {
    color: #2E7D32;
    flex-shrink: 0;
  }
  
  .loyalty-product-info {
    flex: 1;
  }
  
  .loyalty-product-title {
    font-weight: 600;
    color: #2E7D32;
    font-size: 16px;
    margin-bottom: 4px;
  }
  
  .loyalty-product-points {
    display: flex;
    align-items: baseline;
    gap: 6px;
  }
  
  .points-amount {
    font-size: 20px;
    font-weight: 700;
    color: #2E7D32;
  }
  
  .points-label {
    font-size: 14px;
    color: #6c757d;
  }
  
  .loyalty-product-vip {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
  }
  
  .vip-badge {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #333;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
  }
  
  .vip-multiplier {
    font-size: 12px;
    color: #2E7D32;
    font-weight: 500;
  }
  
  .loyalty-product-signup {
    margin-top: 4px;
  }
  
  .loyalty-product-signup span {
    font-size: 12px;
    color: #2E7D32;
    font-weight: 500;
  }
  
  .loyalty-product-arrow {
    color: #2E7D32;
    flex-shrink: 0;
    transition: transform 0.3s ease;
  }
  
  .loyalty-product-widget:hover .loyalty-product-arrow {
    transform: translateX(4px);
  }
  
  .loyalty-product-hover {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    color: white;
    text-align: center;
    padding: 8px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.3s ease;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .loyalty-product-widget {
      padding: 12px;
      margin: 12px 0;
    }
    
    .loyalty-product-title {
      font-size: 14px;
    }
    
    .points-amount {
      font-size: 18px;
    }
    
    .points-label {
      font-size: 13px;
    }
  }
  
  /* Animation au chargement */
  @keyframes loyaltyFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .loyalty-product-widget {
    animation: loyaltyFadeIn 0.6s ease-out;
  }
</style>

<script>
  // Mettre à jour les points en fonction de la variante sélectionnée
  document.addEventListener('DOMContentLoaded', function() {
    const productWidget = document.querySelector('.loyalty-product-widget[data-product-id="{{ product.id }}"]');
    if (!productWidget) return;
    
    // Écouter les changements de variante
    const variantSelectors = document.querySelectorAll('input[name="id"], select[name="id"]');
    variantSelectors.forEach(selector => {
      selector.addEventListener('change', function() {
        updateProductPoints(this.value);
      });
    });
    
    // Écouter les changements de prix (pour les thèmes avec sélecteur de variante custom)
    const priceElements = document.querySelectorAll('.price .money, .price-item .money, [data-price]');
    if (priceElements.length > 0) {
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList' || mutation.type === 'characterData') {
            updateProductPointsFromPrice();
          }
        });
      });
      
      priceElements.forEach(element => {
        observer.observe(element, { childList: true, characterData: true, subtree: true });
      });
    }
    
    function updateProductPoints(variantId) {
      // Récupérer le prix de la variante depuis les données produit
      if (window.productVariants && window.productVariants[variantId]) {
        const price = window.productVariants[variantId].price;
        const points = Math.round(price / 100); // 1 point par euro
        updatePointsDisplay(points);
      }
    }
    
    function updateProductPointsFromPrice() {
      const priceElement = document.querySelector('.price .money, .price-item .money');
      if (priceElement) {
        const priceText = priceElement.textContent.replace(/[^\d.,]/g, '');
        const price = parseFloat(priceText.replace(',', '.')) * 100; // Convertir en centimes
        const points = Math.round(price / 100); // 1 point par euro
        updatePointsDisplay(points);
      }
    }
    
    function updatePointsDisplay(points) {
      const pointsElement = productWidget.querySelector('.points-amount');
      if (pointsElement) {
        pointsElement.textContent = points;
        pointsElement.setAttribute('data-points', points);
        
        // Animation de mise à jour
        pointsElement.style.transform = 'scale(1.1)';
        pointsElement.style.color = '#4CAF50';
        setTimeout(() => {
          pointsElement.style.transform = 'scale(1)';
          pointsElement.style.color = '#2E7D32';
        }, 200);
      }
    }
  });
</script>

{% schema %}
{
  "name": "Widget Fidélité Produit",
  "target": "section",
  "settings": [
    {
      "type": "header",
      "content": "Configuration du widget"
    },
    {
      "type": "checkbox",
      "id": "show_for_guests",
      "label": "Afficher pour les visiteurs non connectés",
      "default": true,
      "info": "Encourage l'inscription au programme"
    },
    {
      "type": "checkbox",
      "id": "show_vip_badge",
      "label": "Afficher le badge VIP",
      "default": true,
      "info": "Affiche le niveau VIP du client connecté"
    },
    {
      "type": "text",
      "id": "custom_title",
      "label": "Titre personnalisé",
      "placeholder": "Gagnez des BROpoints",
      "info": "Laissez vide pour utiliser le titre par défaut"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Couleur d'accent",
      "default": "#2E7D32"
    }
  ]
}
{% endschema %}
