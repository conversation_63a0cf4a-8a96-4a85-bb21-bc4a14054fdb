import prisma from "../db.server";
import { calculateCustomerLevel, getNextLevel } from "../models/VIPLevel.server";
import { triggerLevelUpEmail } from "./emailTriggers.server";

export interface LevelPromotionResult {
  promoted: boolean;
  previousLevel?: string;
  newLevel?: string;
  pointsToNext?: number;
  nextLevelName?: string;
}

/**
 * Check and update customer level based on their current points
 */
export async function checkAndPromoteCustomer(
  customerId: string, 
  shop: string
): Promise<LevelPromotionResult> {
  try {
    // Get customer data
    const customer = await prisma.customer.findUnique({
      where: {
        customerId_shop: {
          customerId,
          shop
        }
      }
    });

    if (!customer) {
      console.warn(`Customer not found: ${customerId} in shop: ${shop}`);
      return { promoted: false };
    }

    // Calculate what level the customer should be at based on their points
    const calculatedLevel = await calculateCustomerLevel(shop, customer.points);
    
    if (!calculatedLevel) {
      console.warn(`No level found for customer with ${customer.points} points`);
      return { promoted: false };
    }

    const previousLevel = customer.vipLevel;
    const newLevel = calculatedLevel.name;

    // Check if promotion is needed
    if (previousLevel === newLevel) {
      // No promotion needed, but return info about next level
      const nextLevel = await getNextLevel(shop, customer.points);
      return {
        promoted: false,
        previousLevel,
        newLevel,
        pointsToNext: nextLevel ? nextLevel.threshold - customer.points : undefined,
        nextLevelName: nextLevel?.name
      };
    }

    // Update customer level
    await prisma.customer.update({
      where: { id: customer.id },
      data: { vipLevel: newLevel }
    });

    console.log(`Customer ${customerId} promoted from ${previousLevel || 'none'} to ${newLevel}`);

    // Get next level info
    const nextLevel = await getNextLevel(shop, customer.points);

    // Trigger level up email notification (async, don't wait)
    if (previousLevel && previousLevel !== newLevel) {
      triggerLevelUpEmail(
        shop,
        customer.id,
        newLevel,
        previousLevel,
        0 // No bonus points for level up in BROpoints system
      ).catch(error => {
        console.error('Error sending level up email:', error);
      });
    }

    return {
      promoted: true,
      previousLevel,
      newLevel,
      pointsToNext: nextLevel ? nextLevel.threshold - customer.points : undefined,
      nextLevelName: nextLevel?.name
    };

  } catch (error) {
    console.error("Error checking customer level promotion:", error);
    return { promoted: false };
  }
}

/**
 * Batch process level promotions for multiple customers
 */
export async function batchPromoteCustomers(shop: string, customerIds: string[]): Promise<LevelPromotionResult[]> {
  const results: LevelPromotionResult[] = [];
  
  for (const customerId of customerIds) {
    const result = await checkAndPromoteCustomer(customerId, shop);
    results.push(result);
  }
  
  return results;
}

/**
 * Get customer level progression info
 */
export async function getCustomerLevelInfo(customerId: string, shop: string) {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        customerId_shop: {
          customerId,
          shop
        }
      }
    });

    if (!customer) {
      return null;
    }

    const currentLevel = await calculateCustomerLevel(shop, customer.points);
    const nextLevel = await getNextLevel(shop, customer.points);

    return {
      currentLevel: currentLevel?.name || 'BRO',
      currentMultiplier: currentLevel?.pointsMultiplier || 1.0,
      currentPoints: customer.points,
      nextLevel: nextLevel?.name,
      pointsToNext: nextLevel ? nextLevel.threshold - customer.points : undefined,
      nextMultiplier: nextLevel?.pointsMultiplier
    };

  } catch (error) {
    console.error("Error getting customer level info:", error);
    return null;
  }
}

/**
 * Initialize customer level when they first join
 */
export async function initializeCustomerLevel(customerId: string, shop: string): Promise<void> {
  try {
    // This will set the customer to the appropriate level based on their current points
    await checkAndPromoteCustomer(customerId, shop);
  } catch (error) {
    console.error("Error initializing customer level:", error);
  }
}

/**
 * Get level multiplier for a customer
 */
export async function getCustomerMultiplier(customerId: string, shop: string): Promise<number> {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        customerId_shop: {
          customerId,
          shop
        }
      }
    });

    if (!customer) {
      return 1.0; // Default multiplier
    }

    const level = await calculateCustomerLevel(shop, customer.points);
    return level?.pointsMultiplier || 1.0;

  } catch (error) {
    console.error("Error getting customer multiplier:", error);
    return 1.0; // Default multiplier on error
  }
}

/**
 * Recalculate all customer levels for a shop (useful for maintenance)
 */
export async function recalculateAllCustomerLevels(shop: string): Promise<number> {
  try {
    const customers = await prisma.customer.findMany({
      where: { shop },
      select: { customerId: true }
    });

    let promotedCount = 0;
    
    for (const customer of customers) {
      const result = await checkAndPromoteCustomer(customer.customerId, shop);
      if (result.promoted) {
        promotedCount++;
      }
    }

    console.log(`Recalculated levels for ${customers.length} customers, ${promotedCount} promoted`);
    return promotedCount;

  } catch (error) {
    console.error("Error recalculating customer levels:", error);
    return 0;
  }
}
