/**
 * Email service for the loyalty program
 * Uses Nodemailer for automatic email sending
 * Support for multiple providers: Gmail, Outlook, custom SMTP
 */

import nodemailer from 'nodemailer';
import type { Transporter } from 'nodemailer';

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

export interface EmailData {
  to: string;
  toName?: string;
  templateData?: Record<string, any>;
}

export interface EmailConfig {
  provider: 'gmail' | 'outlook' | 'smtp' | 'sendgrid';
  fromEmail: string;
  fromName: string;
  enabled: boolean;
  // Configuration Gmail/Outlook OAuth2
  clientId?: string;
  clientSecret?: string;
  refreshToken?: string;
  accessToken?: string;
  // Configuration SMTP personnalisé
  smtpHost?: string;
  smtpPort?: number;
  smtpSecure?: boolean;
  smtpUser?: string;
  smtpPassword?: string;
  // Configuration SendGrid (legacy)
  apiKey?: string;
}

/**
 * Available email types
 */
export enum EmailType {
  WELCOME = 'welcome',
  POINTS_EARNED = 'points_earned',
  POINTS_REDEEMED = 'points_redeemed',
  LEVEL_UP = 'level_up',
  MONTHLY_REMINDER = 'monthly_reminder',
  NEW_PRODUCTS = 'new_products',
  REFERRAL_REWARD = 'referral_reward'
}

/**
 * Default email configuration
 */
function getDefaultEmailConfig(): EmailConfig {
  const provider = (process.env.EMAIL_PROVIDER || 'smtp') as EmailConfig['provider'];

  return {
    provider,
    fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
    fromName: process.env.EMAIL_FROM_NAME || 'BROpoints',
    enabled: process.env.EMAIL_ENABLED === 'true',
    // Gmail/Outlook OAuth2
    clientId: process.env.EMAIL_CLIENT_ID,
    clientSecret: process.env.EMAIL_CLIENT_SECRET,
    refreshToken: process.env.EMAIL_REFRESH_TOKEN,
    accessToken: process.env.EMAIL_ACCESS_TOKEN,
    // SMTP personnalisé
    smtpHost: process.env.SMTP_HOST,
    smtpPort: parseInt(process.env.SMTP_PORT || '587'),
    smtpSecure: process.env.SMTP_SECURE === 'true',
    smtpUser: process.env.SMTP_USER,
    smtpPassword: process.env.SMTP_PASSWORD,
    // SendGrid (legacy)
    apiKey: process.env.SENDGRID_API_KEY
  };
}

/**
 * Get email configuration with database overrides
 */
async function getEmailConfigWithOverrides(shop?: string): Promise<EmailConfig> {
  const defaultConfig = getDefaultEmailConfig();

  if (shop) {
    try {
      const { getSiteSettings } = await import("../models/SiteSettings.server");
      const settings = await getSiteSettings(shop);

      if (settings?.senderEmail) {
        defaultConfig.fromEmail = settings.senderEmail;
      }
    } catch (error) {
      console.warn('Unable to retrieve shop settings:', error);
    }
  }

  return defaultConfig;
}

/**
 * Create a Nodemailer transporter based on the configured provider
 */
function createTransporter(config: EmailConfig): Transporter | null {
  try {
    switch (config.provider) {
      case 'gmail':
        if (!config.clientId || !config.clientSecret || !config.refreshToken) {
          console.error('Incomplete Gmail configuration');
          return null;
        }
        return nodemailer.createTransport({
          service: 'gmail',
          auth: {
            type: 'OAuth2',
            user: config.fromEmail,
            clientId: config.clientId,
            clientSecret: config.clientSecret,
            refreshToken: config.refreshToken,
            accessToken: config.accessToken
          }
        });

      case 'outlook':
        if (!config.clientId || !config.clientSecret || !config.refreshToken) {
          console.error('Incomplete Outlook configuration');
          return null;
        }
        return nodemailer.createTransport({
          service: 'hotmail',
          auth: {
            type: 'OAuth2',
            user: config.fromEmail,
            clientId: config.clientId,
            clientSecret: config.clientSecret,
            refreshToken: config.refreshToken,
            accessToken: config.accessToken
          }
        });

      case 'smtp':
        if (!config.smtpHost || !config.smtpUser || !config.smtpPassword) {
          console.error('Incomplete SMTP configuration');
          return null;
        }
        return nodemailer.createTransport({
          host: config.smtpHost,
          port: config.smtpPort || 587,
          secure: config.smtpSecure || false,
          auth: {
            user: config.smtpUser,
            pass: config.smtpPassword
          },
          // Ignore self-signed certificates in development
          tls: {
            rejectUnauthorized: process.env.NODE_ENV === 'production'
          }
        });

      case 'sendgrid':
        if (!config.apiKey) {
          console.error('Incomplete SendGrid configuration');
          return null;
        }
        return nodemailer.createTransport({
          service: 'SendGrid',
          auth: {
            user: 'apikey',
            pass: config.apiKey
          }
        });

      default:
        console.error('Unsupported email provider:', config.provider);
        return null;
    }
  } catch (error) {
    console.error('Error creating transporter:', error);
    return null;
  }
}

/**
 * Check if the email service is configured
 */
export function isEmailServiceConfigured(): boolean {
  const config = getDefaultEmailConfig();
  if (!config.enabled || !config.fromEmail) {
    return false;
  }
  console.log('isEmailServiceConfigured : ', config);
  // Check according to provider
  switch (config.provider) {
    case 'gmail':
    case 'outlook':
      return !!(config.clientId && config.clientSecret && config.refreshToken);
    case 'smtp':
      return !!(config.smtpHost && config.smtpUser && config.smtpPassword);
    case 'sendgrid':
      return !!config.apiKey;
    default:
      return false;
  }
}

/**
 * Send an email via Nodemailer
 */
export async function sendEmail(
  emailType: EmailType,
  emailData: EmailData,
  customTemplate?: EmailTemplate,
  shop?: string
): Promise<{ success: boolean; error?: string; messageId?: string }> {

  if (!isEmailServiceConfigured()) {
    console.warn('Email service not configured - email not sent');
    return { success: false, error: 'Email service not configured' };
  }

  const config = await getEmailConfigWithOverrides(shop);

  try {
    // Create the transporter
    const transporter = createTransporter(config);
    if (!transporter) {
      return { success: false, error: 'Unable to create email transporter' };
    }

    // Get the email template
    const template = customTemplate || getEmailTemplate(emailType, emailData.templateData);

    // Prepare data for Nodemailer
    const mailOptions = {
      from: {
        name: config.fromName,
        address: config.fromEmail
      },
      to: {
        name: emailData.toName || emailData.to,
        address: emailData.to
      },
      subject: template.subject,
      text: template.textContent,
      html: template.htmlContent
    };

    // Send the email
    const result = await transporter.sendMail(mailOptions);

    console.log(`Email ${emailType} sent successfully to ${emailData.to}`, {
      messageId: result.messageId,
      provider: config.provider
    });

    return {
      success: true,
      messageId: result.messageId
    };

  } catch (error) {
    console.error('Error sending email:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      error: `${config.provider} error: ${errorMessage}`
    };
  }
}

/**
 * Get the email template according to type
 */
function getEmailTemplate(emailType: EmailType, data: Record<string, any> = {}): EmailTemplate {
  const templates: Record<EmailType, (data: Record<string, any>) => EmailTemplate> = {
    [EmailType.WELCOME]: (data) => ({
      subject: `Welcome to the BROpoints program!`,
      htmlContent: getWelcomeEmailHTML(data),
      textContent: getWelcomeEmailText(data)
    }),

    [EmailType.POINTS_EARNED]: (data) => ({
      subject: `You earned ${data.points} BROpoints!`,
      htmlContent: getPointsEarnedEmailHTML(data),
      textContent: getPointsEarnedEmailText(data)
    }),

    [EmailType.POINTS_REDEEMED]: (data) => ({
      subject: `You used ${data.points} BROpoints`,
      htmlContent: getPointsRedeemedEmailHTML(data),
      textContent: getPointsRedeemedEmailText(data)
    }),

    [EmailType.LEVEL_UP]: (data) => ({
      subject: `Congratulations! You are now ${data.newLevel}!`,
      htmlContent: getLevelUpEmailHTML(data),
      textContent: getLevelUpEmailText(data)
    }),

    [EmailType.MONTHLY_REMINDER]: (data) => ({
      subject: `Your BROpoints balance: ${data.points} points`,
      htmlContent: getMonthlyReminderEmailHTML(data),
      textContent: getMonthlyReminderEmailText(data)
    }),

    [EmailType.NEW_PRODUCTS]: (data) => ({
      subject: `New products available in the BROpoints Shop!`,
      htmlContent: getNewProductsEmailHTML(data),
      textContent: getNewProductsEmailText(data)
    }),

    [EmailType.REFERRAL_REWARD]: (data) => ({
      subject: `Your referral earned you ${data.points} points!`,
      htmlContent: getReferralRewardEmailHTML(data),
      textContent: getReferralRewardEmailText(data)
    })
  };

  return templates[emailType](data);
}

/**
 * HTML templates for each email type
 */
function getWelcomeEmailHTML(data: Record<string, any>): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to BROpoints</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .points-badge { background: #4CAF50; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
        .cta-button { background: #2E7D32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Welcome to BROpoints!</h1>
          <p>Your loyalty program starts now</p>
        </div>
        <div class="content">
          <p>Hello ${data.customerName || 'valued customer'},</p>

          <p>Congratulations! You just joined the BROpoints program and have already earned your first points:</p>

          <p style="text-align: center;">
            <span class="points-badge">${data.welcomePoints || 0} points</span>
          </p>

          <h3>How does it work?</h3>
          <ul>
            <li>🛍️ <strong>Earn points</strong> with every purchase</li>
            <li>🎁 <strong>Redeem your points</strong> for rewards</li>
            <li>👥 <strong>Refer your friends</strong> and earn even more</li>
            <li>⭐ <strong>Level up</strong> to unlock exclusive benefits</li>
          </ul>

          <p style="text-align: center; color: white;">
            <a href="${data.shopUrl}" class="cta-button">Discover my rewards</a>
          </p>

          <p>Thank you for your trust and welcome to the BROpoints family!</p>

          <p>The ${data.shopName.split(".")[0] || 'BROpoints'} team</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function getWelcomeEmailText(data: Record<string, any>): string {
  return `
Welcome to BROpoints!

Hello ${data.customerName || 'valued customer'},

Congratulations! You just joined the BROpoints program and have already earned ${data.welcomePoints || 0} points.

How does it work?
- Earn points with every purchase
- Redeem your points for rewards
- Refer your friends and earn even more
- Level up to unlock exclusive benefits

Discover your rewards: ${data.shopUrl}

Thank you for your trust and welcome to the BROpoints family!

The ${data.shopName || 'BROpoints'} team
  `.trim();
}

// Continue with other templates...
function getPointsEarnedEmailHTML(data: Record<string, any>): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Points Earned</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .points-highlight { background: #2E7D32; color: white; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>⭐ Points Earned!</h1>
        </div>
        <div class="content">
          <p>Hello ${data.customerName || 'valued customer'},</p>

          <p>Great news! You just earned BROpoints:</p>

          <div class="points-highlight">
            <h2>+${data.points} points</h2>
            <p>New balance: ${data.totalPoints} points</p>
          </div>

          ${data.reason ? `<p><strong>Reason:</strong> ${data.reason}</p>` : ''}

          <p>Keep shopping to earn even more points and unlock exclusive rewards!</p>

          <p>The ${data.shopName || 'BROpoints'} team</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function getPointsEarnedEmailText(data: Record<string, any>): string {
  return `
Points Earned!

Hello ${data.customerName || 'valued customer'},

Great news! You just earned ${data.points} BROpoints.

New balance: ${data.totalPoints} points

${data.reason ? `Reason: ${data.reason}` : ''}

Keep shopping to earn even more points and unlock exclusive rewards!

The ${data.shopName || 'BROpoints'} team
  `.trim();
}

// Other templates will be added in the continuation...
function getPointsRedeemedEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Points Redeemed</h1><p>You have redeemed ${data.points} points.</p></body></html>`;
}

function getPointsRedeemedEmailText(data: Record<string, any>): string {
  return `Points Redeemed - You have redeemed ${data.points} points.`;
}

function getLevelUpEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Level Up!</h1><p>Congratulations! You are now ${data.newLevel}.</p></body></html>`;
}

function getLevelUpEmailText(data: Record<string, any>): string {
  return `Level Up! Congratulations! You are now ${data.newLevel}.`;
}

function getMonthlyReminderEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Monthly Reminder</h1><p>Your balance: ${data.points} points.</p></body></html>`;
}

function getMonthlyReminderEmailText(data: Record<string, any>): string {
  return `Monthly Reminder - Your balance: ${data.points} points.`;
}

function getNewProductsEmailHTML(_data: Record<string, any>): string {
  return `<html><body><h1>New Products</h1><p>Discover the new products in the BROpoints Shop!</p></body></html>`;
}

function getNewProductsEmailText(_data: Record<string, any>): string {
  return `New Products - Discover the new products in the BROpoints Shop!`;
}

function getReferralRewardEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Referral Rewarded</h1><p>Your referral earned you ${data.points} points!</p></body></html>`;
}

function getReferralRewardEmailText(data: Record<string, any>): string {
  return `Referral Rewarded - Your referral earned you ${data.points} points!`;
}
