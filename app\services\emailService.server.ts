/**
 * Service d'envoi d'emails pour le programme de fidélité
 * Utilise Nodemailer pour l'envoi d'emails automatiques
 * Support de multiples fournisseurs : Gmail, Outlook, SMTP personnalisé
 */

import nodemailer from 'nodemailer';
import type { Transporter } from 'nodemailer';

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

export interface EmailData {
  to: string;
  toName?: string;
  templateData?: Record<string, any>;
}

export interface EmailConfig {
  provider: 'gmail' | 'outlook' | 'smtp' | 'sendgrid';
  fromEmail: string;
  fromName: string;
  enabled: boolean;
  // Configuration Gmail/Outlook OAuth2
  clientId?: string;
  clientSecret?: string;
  refreshToken?: string;
  accessToken?: string;
  // Configuration SMTP personnalisé
  smtpHost?: string;
  smtpPort?: number;
  smtpSecure?: boolean;
  smtpUser?: string;
  smtpPassword?: string;
  // Configuration SendGrid (legacy)
  apiKey?: string;
}

/**
 * Types d'emails disponibles
 */
export enum EmailType {
  WELCOME = 'welcome',
  POINTS_EARNED = 'points_earned',
  POINTS_REDEEMED = 'points_redeemed',
  LEVEL_UP = 'level_up',
  MONTHLY_REMINDER = 'monthly_reminder',
  NEW_PRODUCTS = 'new_products',
  REFERRAL_REWARD = 'referral_reward'
}

/**
 * Configuration par défaut des emails
 */
function getDefaultEmailConfig(): EmailConfig {
  const provider = (process.env.EMAIL_PROVIDER || 'smtp') as EmailConfig['provider'];

  return {
    provider,
    fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
    fromName: process.env.EMAIL_FROM_NAME || 'BROpoints',
    enabled: process.env.EMAIL_ENABLED === 'true',
    // Gmail/Outlook OAuth2
    clientId: process.env.EMAIL_CLIENT_ID,
    clientSecret: process.env.EMAIL_CLIENT_SECRET,
    refreshToken: process.env.EMAIL_REFRESH_TOKEN,
    accessToken: process.env.EMAIL_ACCESS_TOKEN,
    // SMTP personnalisé
    smtpHost: process.env.SMTP_HOST,
    smtpPort: parseInt(process.env.SMTP_PORT || '587'),
    smtpSecure: process.env.SMTP_SECURE === 'true',
    smtpUser: process.env.SMTP_USER,
    smtpPassword: process.env.SMTP_PASSWORD,
    // SendGrid (legacy)
    apiKey: process.env.SENDGRID_API_KEY
  };
}

/**
 * Obtenir la configuration email avec override depuis la base de données
 */
async function getEmailConfigWithOverrides(shop?: string): Promise<EmailConfig> {
  const defaultConfig = getDefaultEmailConfig();

  if (shop) {
    try {
      const { getSiteSettings } = await import("../models/SiteSettings.server");
      const settings = await getSiteSettings(shop);

      if (settings?.senderEmail) {
        defaultConfig.fromEmail = settings.senderEmail;
      }
    } catch (error) {
      console.warn('Impossible de récupérer les paramètres de la boutique:', error);
    }
  }

  return defaultConfig;
}

/**
 * Créer un transporteur Nodemailer selon le fournisseur configuré
 */
function createTransporter(config: EmailConfig): Transporter | null {
  try {
    switch (config.provider) {
      case 'gmail':
        if (!config.clientId || !config.clientSecret || !config.refreshToken) {
          console.error('Configuration Gmail incomplète');
          return null;
        }
        return nodemailer.createTransport({
          service: 'gmail',
          auth: {
            type: 'OAuth2',
            user: config.fromEmail,
            clientId: config.clientId,
            clientSecret: config.clientSecret,
            refreshToken: config.refreshToken,
            accessToken: config.accessToken
          }
        });

      case 'outlook':
        if (!config.clientId || !config.clientSecret || !config.refreshToken) {
          console.error('Configuration Outlook incomplète');
          return null;
        }
        return nodemailer.createTransport({
          service: 'hotmail',
          auth: {
            type: 'OAuth2',
            user: config.fromEmail,
            clientId: config.clientId,
            clientSecret: config.clientSecret,
            refreshToken: config.refreshToken,
            accessToken: config.accessToken
          }
        });

      case 'smtp':
        if (!config.smtpHost || !config.smtpUser || !config.smtpPassword) {
          console.error('Configuration SMTP incomplète');
          return null;
        }
        return nodemailer.createTransport({
          host: config.smtpHost,
          port: config.smtpPort || 587,
          secure: config.smtpSecure || false,
          auth: {
            user: config.smtpUser,
            pass: config.smtpPassword
          },
          // Ignorer les certificats auto-signés en développement
          tls: {
            rejectUnauthorized: process.env.NODE_ENV === 'production'
          }
        });

      case 'sendgrid':
        if (!config.apiKey) {
          console.error('Configuration SendGrid incomplète');
          return null;
        }
        return nodemailer.createTransport({
          service: 'SendGrid',
          auth: {
            user: 'apikey',
            pass: config.apiKey
          }
        });

      default:
        console.error('Fournisseur email non supporté:', config.provider);
        return null;
    }
  } catch (error) {
    console.error('Erreur lors de la création du transporteur:', error);
    return null;
  }
}

/**
 * Vérifier si le service email est configuré
 */
export function isEmailServiceConfigured(): boolean {
  const config = getDefaultEmailConfig();
  if (!config.enabled || !config.fromEmail) {
    return false;
  }
  console.log('isEmailServiceConfigured : ', config);
  // Vérifier selon le fournisseur
  switch (config.provider) {
    case 'gmail':
    case 'outlook':
      return !!(config.clientId && config.clientSecret && config.refreshToken);
    case 'smtp':
      return !!(config.smtpHost && config.smtpUser && config.smtpPassword);
    case 'sendgrid':
      return !!config.apiKey;
    default:
      return false;
  }
}

/**
 * Envoyer un email via Nodemailer
 */
export async function sendEmail(
  emailType: EmailType,
  emailData: EmailData,
  customTemplate?: EmailTemplate,
  shop?: string
): Promise<{ success: boolean; error?: string; messageId?: string }> {

  if (!isEmailServiceConfigured()) {
    console.warn('Service email non configuré - email non envoyé');
    return { success: false, error: 'Service email non configuré' };
  }

  const config = await getEmailConfigWithOverrides(shop);

  try {
    // Créer le transporteur
    const transporter = createTransporter(config);
    if (!transporter) {
      return { success: false, error: 'Impossible de créer le transporteur email' };
    }

    // Obtenir le template d'email
    const template = customTemplate || getEmailTemplate(emailType, emailData.templateData);

    // Préparer les données pour Nodemailer
    const mailOptions = {
      from: {
        name: config.fromName,
        address: config.fromEmail
      },
      to: {
        name: emailData.toName || emailData.to,
        address: emailData.to
      },
      subject: template.subject,
      text: template.textContent,
      html: template.htmlContent
    };

    // Envoyer l'email
    const result = await transporter.sendMail(mailOptions);

    console.log(`Email ${emailType} envoyé avec succès à ${emailData.to}`, {
      messageId: result.messageId,
      provider: config.provider
    });

    return {
      success: true,
      messageId: result.messageId
    };

  } catch (error) {
    console.error('Erreur lors de l\'envoi d\'email:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
    return {
      success: false,
      error: `Erreur ${config.provider}: ${errorMessage}`
    };
  }
}

/**
 * Obtenir le template d'email selon le type
 */
function getEmailTemplate(emailType: EmailType, data: Record<string, any> = {}): EmailTemplate {
  const templates: Record<EmailType, (data: Record<string, any>) => EmailTemplate> = {
    [EmailType.WELCOME]: (data) => ({
      subject: `Bienvenue dans le programme BROpoints !`,
      htmlContent: getWelcomeEmailHTML(data),
      textContent: getWelcomeEmailText(data)
    }),
    
    [EmailType.POINTS_EARNED]: (data) => ({
      subject: `Vous avez gagné ${data.points} points BROpoints !`,
      htmlContent: getPointsEarnedEmailHTML(data),
      textContent: getPointsEarnedEmailText(data)
    }),
    
    [EmailType.POINTS_REDEEMED]: (data) => ({
      subject: `Vous avez utilisé ${data.points} points BROpoints`,
      htmlContent: getPointsRedeemedEmailHTML(data),
      textContent: getPointsRedeemedEmailText(data)
    }),
    
    [EmailType.LEVEL_UP]: (data) => ({
      subject: `Félicitations ! Vous êtes maintenant ${data.newLevel} !`,
      htmlContent: getLevelUpEmailHTML(data),
      textContent: getLevelUpEmailText(data)
    }),
    
    [EmailType.MONTHLY_REMINDER]: (data) => ({
      subject: `Votre solde BROpoints : ${data.points} points`,
      htmlContent: getMonthlyReminderEmailHTML(data),
      textContent: getMonthlyReminderEmailText(data)
    }),
    
    [EmailType.NEW_PRODUCTS]: (data) => ({
      subject: `Nouveaux produits disponibles dans le BROpoints Shop !`,
      htmlContent: getNewProductsEmailHTML(data),
      textContent: getNewProductsEmailText(data)
    }),
    
    [EmailType.REFERRAL_REWARD]: (data) => ({
      subject: `Votre parrainage vous a rapporté ${data.points} points !`,
      htmlContent: getReferralRewardEmailHTML(data),
      textContent: getReferralRewardEmailText(data)
    })
  };

  return templates[emailType](data);
}

/**
 * Templates HTML pour chaque type d'email
 */
function getWelcomeEmailHTML(data: Record<string, any>): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Bienvenue dans BROpoints</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .points-badge { background: #4CAF50; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
        .cta-button { background: #2E7D32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Bienvenue dans BROpoints !</h1>
          <p>Votre programme de fidélité commence maintenant</p>
        </div>
        <div class="content">
          <p>Bonjour ${data.customerName || 'cher client'},</p>
          
          <p>Félicitations ! Vous venez de rejoindre le programme BROpoints et vous avez déjà gagné vos premiers points :</p>
          
          <p style="text-align: center;">
            <span class="points-badge">${data.welcomePoints || 0} points</span>
          </p>
          
          <h3>Comment ça marche ?</h3>
          <ul>
            <li>🛍️ <strong>Gagnez des points</strong> à chaque achat</li>
            <li>🎁 <strong>Échangez vos points</strong> contre des récompenses</li>
            <li>👥 <strong>Parrainez vos amis</strong> et gagnez encore plus</li>
            <li>⭐ <strong>Montez de niveau</strong> pour débloquer des avantages exclusifs</li>
          </ul>
          
          <p style="text-align: center;">
            <a href="${data.shopUrl}" class="cta-button">Découvrir mes récompenses</a>
          </p>
          
          <p>Merci de votre confiance et bienvenue dans la famille BROpoints !</p>
          
          <p>L'équipe ${data.shopName || 'BROpoints'}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function getWelcomeEmailText(data: Record<string, any>): string {
  return `
Bienvenue dans BROpoints !

Bonjour ${data.customerName || 'cher client'},

Félicitations ! Vous venez de rejoindre le programme BROpoints et vous avez déjà gagné ${data.welcomePoints || 0} points.

Comment ça marche ?
- Gagnez des points à chaque achat
- Échangez vos points contre des récompenses  
- Parrainez vos amis et gagnez encore plus
- Montez de niveau pour débloquer des avantages exclusifs

Découvrez vos récompenses : ${data.shopUrl}

Merci de votre confiance et bienvenue dans la famille BROpoints !

L'équipe ${data.shopName || 'BROpoints'}
  `.trim();
}

// Continuer avec les autres templates...
function getPointsEarnedEmailHTML(data: Record<string, any>): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Points gagnés</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .points-highlight { background: #2E7D32; color: white; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>⭐ Points gagnés !</h1>
        </div>
        <div class="content">
          <p>Bonjour ${data.customerName || 'cher client'},</p>
          
          <p>Excellente nouvelle ! Vous venez de gagner des points BROpoints :</p>
          
          <div class="points-highlight">
            <h2>+${data.points} points</h2>
            <p>Nouveau solde : ${data.totalPoints} points</p>
          </div>
          
          ${data.reason ? `<p><strong>Raison :</strong> ${data.reason}</p>` : ''}
          
          <p>Continuez vos achats pour gagner encore plus de points et débloquer des récompenses exclusives !</p>
          
          <p>L'équipe ${data.shopName || 'BROpoints'}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function getPointsEarnedEmailText(data: Record<string, any>): string {
  return `
Points gagnés !

Bonjour ${data.customerName || 'cher client'},

Excellente nouvelle ! Vous venez de gagner ${data.points} points BROpoints.

Nouveau solde : ${data.totalPoints} points

${data.reason ? `Raison : ${data.reason}` : ''}

Continuez vos achats pour gagner encore plus de points et débloquer des récompenses exclusives !

L'équipe ${data.shopName || 'BROpoints'}
  `.trim();
}

// Les autres templates seront ajoutés dans la suite...
function getPointsRedeemedEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Points utilisés</h1><p>Vous avez utilisé ${data.points} points.</p></body></html>`;
}

function getPointsRedeemedEmailText(data: Record<string, any>): string {
  return `Points utilisés - Vous avez utilisé ${data.points} points.`;
}

function getLevelUpEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Niveau supérieur !</h1><p>Félicitations ! Vous êtes maintenant ${data.newLevel}.</p></body></html>`;
}

function getLevelUpEmailText(data: Record<string, any>): string {
  return `Niveau supérieur ! Félicitations ! Vous êtes maintenant ${data.newLevel}.`;
}

function getMonthlyReminderEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Rappel mensuel</h1><p>Votre solde : ${data.points} points.</p></body></html>`;
}

function getMonthlyReminderEmailText(data: Record<string, any>): string {
  return `Rappel mensuel - Votre solde : ${data.points} points.`;
}

function getNewProductsEmailHTML(_data: Record<string, any>): string {
  return `<html><body><h1>Nouveaux produits</h1><p>Découvrez les nouveaux produits dans le BROpoints Shop !</p></body></html>`;
}

function getNewProductsEmailText(_data: Record<string, any>): string {
  return `Nouveaux produits - Découvrez les nouveaux produits dans le BROpoints Shop !`;
}

function getReferralRewardEmailHTML(data: Record<string, any>): string {
  return `<html><body><h1>Parrainage récompensé</h1><p>Votre parrainage vous a rapporté ${data.points} points !</p></body></html>`;
}

function getReferralRewardEmailText(data: Record<string, any>): string {
  return `Parrainage récompensé - Votre parrainage vous a rapporté ${data.points} points !`;
}
