{% comment %}
  Bloc Liquid : Widget de fidélité pour page Thank You
  Affiche les points gagnés avec la commande et encourage l'engagement
{% endcomment %}

{% if order %}
  {% assign order_total = order.total_price %}
  {% assign earned_points = order_total | divided_by: 100 | round %}
{% else %}
  {% assign earned_points = 0 %}
{% endif %}

<div class="loyalty-thankyou-widget" 
     onclick="window.loyaltyWidget && window.loyaltyWidget.open()">
  
  {% if customer %}
    <!-- Client connecté -->
    <div class="loyalty-thankyou-content">
      <div class="loyalty-thankyou-header">
        <div class="loyalty-celebration">
          <div class="celebration-icon">🎉</div>
          <div class="celebration-text">
            <h3>Félicitations !</h3>
            <p>Vous avez gagné des BROpoints</p>
          </div>
        </div>
      </div>
      
      <div class="loyalty-points-earned">
        <div class="points-circle">
          <div class="points-number" data-earned-points="{{ earned_points }}">+{{ earned_points }}</div>
          <div class="points-label">points</div>
        </div>
        
        <div class="points-details">
          {% if order %}
            <div class="order-info">
              <span class="order-label">Commande #{{ order.order_number }}</span>
              <span class="order-amount">{{ order.total_price | money }}</span>
            </div>
          {% endif %}
          
          {% if customer.tags contains 'vip' %}
            <div class="vip-bonus">
              <span class="vip-badge">{{ customer.metafields.loyalty.vip_level | default: 'VIP' }}</span>
              <span class="vip-text">Bonus appliqué</span>
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="loyalty-next-steps">
        <div class="current-balance">
          <span class="balance-label">Votre nouveau solde :</span>
          <span class="balance-value" id="thankyou-balance">-- points</span>
        </div>
        
        <div class="next-reward">
          <span class="reward-label">Prochaine récompense :</span>
          <span class="reward-info" id="thankyou-next-reward">Chargement...</span>
        </div>
      </div>
      
      <div class="loyalty-actions">
        <div class="action-primary">
          <span class="action-text">Découvrir mes récompenses</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        
        <div class="action-secondary">
          <div class="share-referral">
            <span class="share-icon">👥</span>
            <span>Parrainez un ami et gagnez plus de points</span>
          </div>
        </div>
      </div>
    </div>
    
  {% else %}
    <!-- Client non connecté -->
    <div class="loyalty-thankyou-content guest">
      <div class="loyalty-thankyou-header">
        <div class="loyalty-celebration">
          <div class="celebration-icon">💡</div>
          <div class="celebration-text">
            <h3>Vous auriez pu gagner {{ earned_points }} points !</h3>
            <p>Rejoignez BROpoints pour vos prochains achats</p>
          </div>
        </div>
      </div>
      
      <div class="loyalty-missed-opportunity">
        <div class="missed-points">
          <div class="missed-number">{{ earned_points }}</div>
          <div class="missed-label">points manqués</div>
        </div>
        
        <div class="future-benefits">
          <div class="benefit-item">
            <span class="benefit-icon">⭐</span>
            <span>Points à chaque achat</span>
          </div>
          <div class="benefit-item">
            <span class="benefit-icon">🎁</span>
            <span>Récompenses exclusives</span>
          </div>
          <div class="benefit-item">
            <span class="benefit-icon">👥</span>
            <span>Bonus de parrainage</span>
          </div>
        </div>
      </div>
      
      <div class="loyalty-actions">
        <div class="action-primary">
          <span class="action-text">Rejoindre BROpoints maintenant</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        
        <div class="signup-incentive">
          <span class="incentive-text">Inscription gratuite • Points immédiats</span>
        </div>
      </div>
    </div>
  {% endif %}
</div>

<style>
  .loyalty-thankyou-widget {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #2E7D32;
    border-radius: 20px;
    padding: 24px;
    margin: 24px 0;
    cursor: pointer;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
  }
  
  .loyalty-thankyou-widget::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(46, 125, 50, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
  }
  
  .loyalty-thankyou-widget:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
  }
  
  @keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  }
  
  .loyalty-thankyou-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(46, 125, 50, 0.2);
    border-color: #4CAF50;
  }
  
  .loyalty-thankyou-content {
    position: relative;
    z-index: 1;
  }
  
  .loyalty-thankyou-header {
    text-align: center;
    margin-bottom: 24px;
  }
  
  .loyalty-celebration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
  }
  
  .celebration-icon {
    font-size: 48px;
    animation: bounce 2s infinite;
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
  }
  
  .celebration-text h3 {
    margin: 0 0 8px 0;
    color: #2E7D32;
    font-size: 24px;
    font-weight: 700;
  }
  
  .celebration-text p {
    margin: 0;
    color: #6c757d;
    font-size: 16px;
  }
  
  .loyalty-points-earned {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    border-radius: 16px;
    color: white;
  }
  
  .points-circle {
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    padding: 20px;
    min-width: 120px;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .points-number {
    font-size: 32px;
    font-weight: 900;
    line-height: 1;
  }
  
  .points-label {
    font-size: 14px;
    opacity: 0.9;
    margin-top: 4px;
  }
  
  .points-details {
    flex: 1;
  }
  
  .order-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 12px;
  }
  
  .order-label {
    font-size: 14px;
    opacity: 0.9;
  }
  
  .order-amount {
    font-size: 18px;
    font-weight: 600;
  }
  
  .vip-bonus {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .vip-badge {
    background: #FFD700;
    color: #333;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
  }
  
  .vip-text {
    font-size: 14px;
    opacity: 0.9;
  }
  
  .loyalty-next-steps {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
  }
  
  .current-balance,
  .next-reward {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .next-reward {
    margin-bottom: 0;
  }
  
  .balance-label,
  .reward-label {
    color: #6c757d;
    font-size: 14px;
  }
  
  .balance-value,
  .reward-info {
    color: #2E7D32;
    font-weight: 600;
    font-size: 16px;
  }
  
  .loyalty-actions {
    text-align: center;
  }
  
  .action-primary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
  }
  
  .loyalty-thankyou-widget:hover .action-primary {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(46, 125, 50, 0.3);
  }
  
  .action-secondary {
    color: #6c757d;
    font-size: 14px;
  }
  
  .share-referral {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .share-icon {
    font-size: 16px;
  }
  
  /* Version invité */
  .loyalty-missed-opportunity {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    border-radius: 16px;
    color: white;
  }
  
  .missed-points {
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    padding: 20px;
    min-width: 120px;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .missed-number {
    font-size: 32px;
    font-weight: 900;
    line-height: 1;
  }
  
  .missed-label {
    font-size: 14px;
    opacity: 0.9;
    margin-top: 4px;
  }
  
  .future-benefits {
    flex: 1;
  }
  
  .benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 14px;
  }
  
  .benefit-item:last-child {
    margin-bottom: 0;
  }
  
  .benefit-icon {
    font-size: 18px;
    flex-shrink: 0;
  }
  
  .signup-incentive {
    color: #6c757d;
    font-size: 14px;
    font-style: italic;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .loyalty-thankyou-widget {
      padding: 20px;
      margin: 20px 0;
    }
    
    .loyalty-celebration {
      flex-direction: column;
      gap: 12px;
    }
    
    .celebration-text h3 {
      font-size: 20px;
    }
    
    .loyalty-points-earned,
    .loyalty-missed-opportunity {
      flex-direction: column;
      gap: 16px;
    }
    
    .points-circle,
    .missed-points {
      min-width: 100px;
      min-height: 100px;
      padding: 16px;
    }
    
    .points-number,
    .missed-number {
      font-size: 28px;
    }
    
    .current-balance,
    .next-reward {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
</style>

<script>
  // Mettre à jour les informations de fidélité sur la page Thank You
  document.addEventListener('DOMContentLoaded', function() {
    const thankyouWidget = document.querySelector('.loyalty-thankyou-widget');
    if (!thankyouWidget) return;
    
    // Mettre à jour le solde et la prochaine récompense
    function updateThankyouInfo() {
      if (window.loyaltyWidget && window.loyaltyWidget.customerData) {
        const customerData = window.loyaltyWidget.customerData;
        const earnedPoints = parseInt(thankyouWidget.querySelector('[data-earned-points]')?.getAttribute('data-earned-points') || '0');
        const newBalance = (customerData.points || 0) + earnedPoints;
        
        // Mettre à jour le solde
        const balanceElement = document.getElementById('thankyou-balance');
        if (balanceElement) {
          balanceElement.textContent = `${newBalance} points`;
        }
        
        // Calculer la prochaine récompense
        const nextRewardElement = document.getElementById('thankyou-next-reward');
        if (nextRewardElement) {
          const nextRewardPoints = Math.ceil(newBalance / 100) * 100; // Prochaine centaine
          const pointsNeeded = nextRewardPoints - newBalance;
          
          if (pointsNeeded <= 0) {
            nextRewardElement.textContent = 'Récompense disponible !';
            nextRewardElement.style.color = '#4CAF50';
          } else {
            nextRewardElement.textContent = `${pointsNeeded} points pour ${nextRewardPoints / 100}€`;
          }
        }
      }
    }
    
    // Animation des points gagnés
    function animatePointsEarned() {
      const pointsNumber = thankyouWidget.querySelector('.points-number');
      if (pointsNumber) {
        const finalPoints = parseInt(pointsNumber.getAttribute('data-earned-points') || '0');
        let currentPoints = 0;
        const increment = Math.max(1, Math.floor(finalPoints / 20));
        
        const animation = setInterval(() => {
          currentPoints += increment;
          if (currentPoints >= finalPoints) {
            currentPoints = finalPoints;
            clearInterval(animation);
          }
          pointsNumber.textContent = `+${currentPoints}`;
        }, 50);
      }
    }
    
    // Initialiser
    setTimeout(() => {
      updateThankyouInfo();
      animatePointsEarned();
    }, 1000);
    
    // Écouter les mises à jour du widget de fidélité
    window.addEventListener('loyaltyPointsUpdated', updateThankyouInfo);
  });
</script>

{% schema %}
{
  "name": "Widget Fidélité Thank You",
  "target": "section",
  "settings": [
    {
      "type": "header",
      "content": "Configuration du widget"
    },
    {
      "type": "checkbox",
      "id": "show_for_guests",
      "label": "Afficher pour les visiteurs non connectés",
      "default": true,
      "info": "Encourage l'inscription au programme"
    },
    {
      "type": "checkbox",
      "id": "show_next_reward",
      "label": "Afficher la prochaine récompense",
      "default": true,
      "info": "Montre les points nécessaires pour la prochaine récompense"
    },
    {
      "type": "checkbox",
      "id": "show_referral_prompt",
      "label": "Afficher l'invitation au parrainage",
      "default": true,
      "info": "Encourage le parrainage d'amis"
    },
    {
      "type": "text",
      "id": "celebration_message",
      "label": "Message de félicitations",
      "placeholder": "Félicitations !",
      "info": "Message affiché aux clients connectés"
    }
  ]
}
{% endschema %}
