{"loyalty": {"points": {"balance": "Pontos disponíveis", "earn": "Ganhe {{points}} pontos", "spend": "Use seus pontos"}, "referral": {"invite": "Convide um amigo", "reward": "Ganhe {{points}} pontos por cada indicação"}, "vip": {"status": "Status VIP", "progress": "Progresso para status VIP"}}, "common": {"save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "loading": "Carregando...", "error": "Ocorreu um erro", "search": "Pesquisar...", "logout": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "add": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "view": "Visualizar", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "<PERSON>m", "no": "Não", "active": "Ativo", "inactive": "Inativo", "enabled": "Habilitado", "disabled": "Desabilitado", "success": "Sucesso", "warning": "Aviso", "info": "Informação", "name": "Nome", "description": "Descrição", "status": "Status", "actions": "Ações", "settings": "Configurações", "configuration": "Configuração"}, "admin": {"navigation": {"dashboard": "<PERSON><PERSON>", "program": "Programa", "customers": "Clientes", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Configurações", "promotions": "Promoções", "history": "Hist<PERSON><PERSON><PERSON>", "pointsShop": "Loja de pontos", "overview": "Visão geral", "pointsConfig": "Configuração de pontos", "referrals": "Indicações", "vipProgram": "Programa VIP", "bonusCampaigns": "<PERSON><PERSON><PERSON>", "generalSettings": "Configurações gerais", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "<PERSON><PERSON><PERSON> re<PERSON>"}, "dashboard": {"title": "<PERSON><PERSON>", "totalPoints": "Pontos totais", "activeMembers": "Membros ativos", "redemptionRate": "Taxa de resgate", "pointsEvolution": "Evolução dos pontos (30 dias)", "pointsDistribution": "Distribuição de pontos por tipo", "recentActivity": "Atividade recente", "loadingChart": "Carregando gráfico...", "tableHeaders": {"customer": "Cliente", "action": "Ação", "points": "Pontos", "date": "Data"}, "averagePointsPerCustomer": "Pontos médios / cliente", "dataAvailableSoon": "Os dados estarão disponíveis em breve...", "pointsInCirculation": "Pontos em circulação", "rewardsThisMonth": "Recompensas este mês"}, "program": {"title": "Programa de fidelidade", "overview": "Visão geral", "status": {"active": "Programa ativo", "inactive": "Programa inativo", "activate": "Ativar", "deactivate": "Desativar", "activeDescription": "Seu programa de fidelidade está atualmente ativo e seus clientes podem ganhar pontos.", "inactiveDescription": "Seu programa está atualmente inativo. Ative-o para permitir que seus clientes ganhem pontos."}, "generalConfiguration": "Configuração geral", "programName": "Nome do programa", "programDescription": "Descrição do programa", "quickActions": "Ações <PERSON>", "pointsConfiguration": "Configuração de pontos", "referralProgram": "Programa de indicações", "stats": {"title": "Estatísticas do programa", "totalCustomers": "Clientes totais", "activeCustomers": "Clientes ativos", "totalPointsEarned": "Pontos ganhos totais", "totalPointsRedeemed": "Pontos resgatados totais", "totalRewards": "Recompensas totais", "pendingReferrals": "Indicações pendentes", "completedReferrals": "Indicações concluídas", "pointsDistributed": "Pontos distribuídos"}, "paramSaveSuccess": "Parâmetros salvos com sucesso", "paramSaveError": "Erro ao salvar parâmet<PERSON>", "saveModifications": "Salvar modificaçõ<PERSON>", "saveDescription": "Você tem modificações não salvas"}, "customers": {"title": "Clientes", "member": "Membro", "guest": "Convidado", "points": "pontos", "referrals": "pessoa(s)", "email": "Email", "joinedOn": "<PERSON><PERSON><PERSON><PERSON> em", "type": "Tipo", "filters": {"type": "Tipo", "search": "Pesquisar por nome ou email"}, "pagination": "Página {{current}} de {{total}}", "back": "Voltar", "viewInShopify": "Ver no Shopify", "infoTitle": "Informações do cliente", "activityTitle": "Atividade", "pointsTab": "Pontos", "referralsTab": "Indicações", "rewardsTab": "Recompensas", "ordersTitle": "Pedidos", "orderId": "ID do pedido", "total": "Total", "status": "Status", "date": "Data", "noOrders": "Nenhum pedido", "currentBalance": "<PERSON><PERSON>", "statsTitle": "Estatísticas", "totalSpent": "Total gasto", "ordersCount": "Pedidos", "completedReferrals": "Indicações", "referralTitle": "Indicação", "referralFeatureComing": "Funcionalidade em desenvolvimento", "referralCodeInfo": "Código de indicação e link serão exibidos aqui", "action": "Ação", "referee": "Indicado", "referralStatus": "Status", "referralOrderTotal": "Total do pedido", "reward": "Recompensa", "code": "Código", "noRewards": "Nenhuma recompensa resgatada", "none": "—", "earned": "<PERSON><PERSON><PERSON>", "redeemed": "Resgatado", "signup": "Cadastro", "validated": "Validado", "pending": "Pendente", "paid": "Pago", "anonymous": "Cliente anônimo", "client": "Cliente", "errors": {"customerIdRequired": "ID do cliente obrigatório", "customerNotFound": "Cliente não encontrado", "actionNotRecognized": "Ação não reconhecida"}, "success": {"referralLinkGenerated": "Link de indicação gerado com sucesso"}, "referralInterface": {"currentReferralLink": "Link de indicação atual", "code": "Código", "expiresOn": "Expira em", "copied": "Copiado!", "copyLink": "Copiar link", "noActiveReferralLink": "Nenhum link de indicação ativo", "generateReferralLink": "Gerar link de indicação", "referralTableHeaders": {"referee": "Indicado", "code": "Código", "status": "Status", "validationDate": "Data de validação", "expirationDate": "Data de expiração", "pointsEarned": "Pontos ganhos"}, "referralStatuses": {"pending": "Pendente", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>"}, "referralStats": {"title": "Estatísticas de Indicações", "referralsSent": "Indicações enviadas", "referralsValidated": "Indicações validadas", "pending": "Pendente", "pointsEarned": "Pontos ganhos", "currentLink": "<PERSON> atual", "activeUntil": "Ativo até", "referredBy": "Indicado por"}}}, "analytics": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Análises do programa de fidelidade", "memberStats": "Estatísticas de membros", "totalMembers": "<PERSON><PERSON><PERSON>", "newMembersLast30Days": "Novos membros (30 dias)", "pointsTransactions": "Transações de pontos", "totalTransactions": "Transações totais", "pointsDistributed": "Pontos distribuídos", "referralPurchases": "Compras por indicação", "referralRevenue": "Receita por indicação", "trends": "Tendências", "membersGrowth": "Crescimento de membros", "pointsGrowth": "Crescimento de pontos", "revenueGrowth": "Crescimento de receita"}, "settings": {"title": "Configurações", "quickNavigation": "Navegação rápida", "customizeWidget": "<PERSON><PERSON>r widget", "exchangeableProducts": "<PERSON><PERSON><PERSON> re<PERSON>", "generalSettings": "Configurações gerais", "shopName": "Nome da loja", "currency": "<PERSON><PERSON>", "language": "Idioma", "emailNotifications": "Notificações por email", "pointsName": "Nome dos pontos", "welcomeMessage": "Mensagem de boas-vindas", "saveSuccess": "Configurações salvas com sucesso", "saveError": "Erro ao salvar configurações", "customizationTitle": "Personalização", "pointsNameHelp": "Este nome será usado em toda a aplicação", "welcomeMessageHelp": "Mensagem exibida para novos clientes", "notificationsTitle": "Notificações", "senderEmail": "Email do remetente", "saveCardTitle": "💾 Salvar alterações", "saveButton": "<PERSON><PERSON>", "noChanges": "Sem alterações", "unsavedChanges": "Você tem alterações não salvas", "senderEmailHelp": "Deixe vazio para usar configuração padrão"}, "emails": {"title": "Configuração de E-mails", "subtitle": "Gerencie notificações por e-mail do programa de fidelidade", "backToSettings": "Configurações", "configurationStatus": "Status da Configuração", "emailService": "Serviço de e-mail", "configured": "<PERSON><PERSON><PERSON><PERSON>", "notConfigured": "Não configurado", "notificationsEnabled": "Notificações ativadas", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON><PERSON>", "generalSettings": "Configurações Gerais", "enableNotifications": "Ativar notificações por e-mail", "enableNotificationsHelp": "Clientes receberão e-mails para eventos do programa de fidelidade", "shopName": "Nome da loja", "shopNameHelp": "Nome exibido nos e-mails", "pointsName": "Nome dos pontos", "pointsNameHelp": "Nome usado para pontos nos e-mails (ex: BROpoints)", "automaticEmailTypes": "Tipos de E-mails Automáticos", "welcomeEmail": "E-mail de boas-vindas", "welcomeEmailDesc": "Enviado ao ingressar no programa", "pointsEarned": "Pontos ganhos", "pointsEarnedDesc": "Enviado quando cliente ganha pontos", "pointsUsed": "Pontos usados", "pointsUsedDesc": "Enviado quando cliente usa pontos", "levelPromotion": "Promoção de nível", "levelPromotionDesc": "Enviado quando nível <PERSON> muda", "referralReward": "Recompensa de indicação", "referralRewardDesc": "Enviado quando indicação é bem-sucedida", "active": "Ativo", "actions": "Ações", "saveSettings": "Salvar configuraçõ<PERSON>", "sendTestEmail": "Enviar e-mail de teste", "testEmailRequirement": "E-mail de teste requer que o serviço esteja configurado e ativado", "configuredProvider": "<PERSON><PERSON><PERSON> configurado"}, "points": {"title": "Configuração de pontos", "waysToEarn": "Formas de ganhar pontos", "waysToRedeem": "Formas de resgatar pontos", "addWayToEarn": "Adicionar forma de ganhar", "addWayToRedeem": "Adicionar forma de resgatar", "earnDescription": "Configure as diferentes formas que seus clientes podem ganhar pontos. Você pode criar ações com pontos por euro gasto (ex: 5 pontos/€1) ou pontos fixos para ações específicas (ex: 100 pontos para registro).", "redeemDescription": "Configure as recompensas que seus clientes podem obter em troca de seus pontos.", "minPoints": "A partir de {{points}} pontos", "exactPoints": "{{points}} pontos", "configurable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fixed": "Fixo", "baseSettings": "Configurações básicas", "earningRateLabel": "Taxa de ganho (pontos/€)", "redemptionRateLabel": "Taxa de conversão (pontos/€)", "redemptionRateHelp": "Número de pontos necessários para 1€ de desconto (ex: 100 pontos = 1€)", "minimumPointsLabel": "Pontos mínimos para resgatar", "expirationDaysLabel": "Expiração dos pontos (dias)", "referralPointsLabel": "Pontos por indicação", "birthdayPointsLabel": "Pontos de aniversário", "save": "<PERSON><PERSON>", "previewTitle": "Pré-visualização", "previewAmountLabel": "<PERSON><PERSON> da compra (€)", "previewForAmount": "Para uma compra de {amount}€:", "previewPoints": "Pontos ganhos: {points} pontos", "previewValue": "Valor em €: {value}€", "waysToEarnTitle": "Maneiras de ganhar pontos", "waysToEarnDescription": "Configure as diferentes maneiras pelas quais seus clientes podem ganhar pontos. Você pode criar ações com pontos por euro gasto (ex: 5 pontos/€1) ou pontos fixos para ações específicas (ex: 100 pontos por cadastro).", "fixedPoints": "{points} pontos fixos", "pointsPerEuro": "{points} pontos por €1 gasto", "active": "Ativo", "inactive": "Inativo", "edit": "<PERSON><PERSON>", "seeAllWaysToEarn": "<PERSON><PERSON> as man<PERSON><PERSON> de g<PERSON><PERSON>", "seeAllWaysToEarnCount": "<PERSON><PERSON> <PERSON><PERSON> as man<PERSON><PERSON> de g<PERSON> ({count})", "waysToRedeemTitle": "Maneiras de resgatar pontos", "waysToRedeemDescription": "Configure as diferentes recompensas que seus clientes podem obter ao resgatar seus pontos. Por exemplo, descontos em pedidos, produtos gratuitos ou frete gr<PERSON>tis.", "fromPoints": "A partir de {points} pontos", "pointsCost": "{points} pontos", "seeAllWaysToRedeem": "<PERSON><PERSON><PERSON> as man<PERSON><PERSON> de resgatar", "seeAllWaysToRedeemCount": "<PERSON><PERSON> <PERSON><PERSON> as man<PERSON><PERSON> de resgatar ({count})", "successUpdate": "Configurações atualizadas com sucesso", "errorUpdate": "<PERSON>rro ao atualizar as configurações", "errorAllFieldsRequired": "Todos os campos são obrigatórios", "errorAllNumbers": "Todos os valores devem ser números válidos", "errorPositiveValues": "Os valores devem ser positivos"}, "referrals": {"title": "Programa de indicação", "description": "O programa de indicação permite que seus clientes fiéis recomendem sua loja para amigos e familiares. Quando um cliente indica um amigo que faz a primeira compra, ambos recebem recompensas. É uma ótima maneira de conquistar novos clientes e recompensar a fidelidade dos atuais.", "howItWorks": "Como funciona:", "step1": "O indicador compartilha seu código de indicação único com amigos", "step2": "O indicado usa esse código em seu primeiro pedido", "step3": "Se o pedido atingir o valor mínimo, as recompensas são distribuídas", "step4": "Tanto o indicador quanto o indicado recebem suas respectivas recompensas", "programStatus": "Status do programa", "active": "Ativo", "inactive": "Inativo", "activate": "Ativar", "deactivate": "Desativar", "activeDescription": "Seus clientes podem atualmente indicar amigos e ganhar recompensas.", "inactiveDescription": "O programa de indicação está atualmente desativado. Ative para permitir que seus clientes indiquem amigos.", "referrerRewardTitle": "Recompensa do indicador", "referrerGets": "O indicador recebe {reward}", "referredRewardTitle": "Recompensa do indicado", "referredGets": "O indicado recebe {reward}", "rewardTypeLabel": "Tipo de recompensa", "rewardTypePoints": "Pontos", "rewardTypeFixed": "Desconto fixo (€)", "rewardTypeDiscount": "Percentual (%)", "rewardAmountPoints": "Quantidade em pontos", "rewardAmountFixed": "Valor do desconto (€)", "rewardAmountDiscount": "Percentual de desconto (%)", "rewardAmountDefault": "<PERSON><PERSON> da recompensa", "conditionsTitle": "Condições", "minPurchaseLabel": "Valor mínimo de compra para o indicado (€)", "minPurchaseHelp": "<PERSON>or mínimo que o indicado deve gastar para validar a indicação", "expiryDaysLabel": "Validade do convite (dias)", "expiryDaysHelp": "Número de dias que o convite permanece válido", "customizationTitle": "Personalização", "customMessageLabel": "Mensagem de convite", "customMessageHelp": "Esta mensagem será exibida na página de indicação", "save": "<PERSON><PERSON>", "referralLinksTitle": "Gestão de links de indicação", "referralLinksDescription": "Gere e gerencie os links de indicação para seus clientes.", "customerTableName": "Nome", "customerTableEmail": "Email", "customerTableType": "Tipo", "customerTablePoints": "Pontos", "customerTableStatus": "Status da indicação", "customerTableAction": "Ação", "member": "Membro", "guest": "Convidado", "linkActive": "Link ativo", "noLink": "Sem link", "generateLink": "Gerar link", "existingLink": "Link existente", "noCustomers": "Nenhum cliente encontrado. Certifique-se de ter clientes em sua loja.", "linkFormatTitle": "Formato dos links de indicação", "linkFormatDescription": "<PERSON>s links gerados seguem este formato:", "linkFormatExample": "https://sua-loja.myshopify.com?ref=eyxxxxxxxxxxxxxxxx", "linkFormatHelp": "Onde \"eyxxxxxxxxxxxxxxxx\" é um token único seguro em base64.", "linkHowItWorks": "Como funciona:", "linkStep1": "O cliente gera seu link pelo widget de fidelidade", "linkStep2": "Compartilha o link nas redes sociais ou por email", "linkStep3": "Um amigo clica no link e é redirecionado para sua loja", "linkStep4": "O código é capturado e armazenado automaticamente", "linkStep5": "Na compra, a indicação é validada e as recompensas distribuídas", "statsTitle": "Estatísticas de indicação", "statsTotal": "Total de indicações", "statsCompleted": "Completas", "statsPending": "Pendentes", "statsConversion": "Taxa de conversão", "helpTitle": "<PERSON><PERSON><PERSON>", "helpDescription1": "O programa de indicação permite que seus clientes recomendem sua loja para amigos. Indicadores e indicados recebem recompensas quando a indicação é validada.", "helpDescription2": "Uma indicação é validada quando o indicado faz a primeira compra atingindo o valor mínimo definido."}, "widget": {"title": "Personalização do widget", "appearance": "Aparência", "colors": "Cores", "position": {"label": "Posição do widget", "bottomRight": "Inferior direita", "bottomLeft": "Inferior esquerda", "topRight": "Superior direita", "topLeft": "Superior esquerda"}, "size": {"label": "Tamanho do widget", "small": "Pequeno", "medium": "Médio", "large": "Grande"}, "borders": {"label": "<PERSON><PERSON><PERSON>", "square": "Quadrado", "rounded": "<PERSON><PERSON><PERSON><PERSON>", "pill": "<PERSON><PERSON><PERSON><PERSON>"}, "shadow": "Sombra", "animation": "Animação", "showPointsOnButton": "Mostrar pontos no botão", "primaryColor": "<PERSON><PERSON>", "secondaryColor": "<PERSON><PERSON>", "textColor": "Cor do texto", "preview": "Visualização", "previewDescription": "Visualização em tempo real do seu widget", "loyaltyProgram": "Programa de Fidelidade", "welcomeTo": "Bem-vindo ao programa de fidelidade", "welcomeMessage": "Bem-vindo ao nosso programa de fidelidade!", "guest": "Convidado", "member": "Membro", "points": "Pontos", "orders": "Pedidos", "nextReward": "Próxima recompensa", "pointsNeeded": "Faltam {{count}} pontos", "yourRewards": "Suas recompensas", "oneRewardAvailable": "Você tem 1 recompensa disponível", "waysToEarn": "Maneiras de ganhar", "waysToRedeem": "Maneiras de resgatar", "referFriends": "Indique seus amigos", "referralsCompleted": "{{count}} indicações concluídas", "shareUrl": "Compartilhe esta URL para dar aos seus amigos um cupom de €4", "facebook": "Facebook", "x": "X", "email": "Email", "yourActivity": "Sua atividade", "poweredBy": "Powered by <PERSON><PERSON><PERSON>"}, "notifications": {"languageChanged": "Idioma alterado para {{language}}"}, "exchangeableProducts": {"title": "<PERSON><PERSON><PERSON> re<PERSON>", "subtitle": "Gerencie os produtos que seus clientes podem obter em troca de pontos", "addProduct": "Ad<PERSON><PERSON><PERSON> produto", "emptyStateHeading": "Nenhum produto resgatável configurado", "emptyStateDescription": "Comece adicionando produtos que seus clientes possam resgatar por pontos.", "product": "Produ<PERSON>", "image": "Imagem", "pointsCost": "Custo em pontos", "status": "Status", "actions": "Ações", "calculatedAuto": "Calculado automaticamente", "active": "Ativo", "inactive": "Inativo", "activate": "Ativar", "deactivate": "Desativar", "delete": "Excluir", "successAdd": "Adicionando {count} produto(s)...", "successDelete": "Produto resgatável excluído com sucesso", "successToggle": "Status do produto {status} com sucesso", "errorSelectOne": "Selecione pelo menos um produto", "modalTitle": "Adicionar produto res<PERSON>", "modalPrimary": "<PERSON><PERSON><PERSON><PERSON>", "modalSecondary": "<PERSON><PERSON><PERSON>", "modalDescription": "Selecione os produtos que seus clientes poderão resgatar por pontos. O custo em pontos será definido nas configurações do programa.", "selectedCount": "{count} produto(s) selecionado(s). O custo em pontos será configurado automaticamente conforme as configurações do programa."}, "productSelector": {"title": "Produtos resgatáveis com pontos", "addProducts": "<PERSON><PERSON><PERSON><PERSON> produtos", "description": "Selecione os produtos que seus clientes podem comprar com seus pontos de fidelidade.", "empty": "Nenhum produto selecionado. Clique em 'Adicionar produtos' para começar.", "price": "Preço", "remove": "Remover", "selectProducts": "Selecionar produtos", "close": "<PERSON><PERSON><PERSON>", "searchLabel": "Pes<PERSON><PERSON> produtos", "searchPlaceholder": "Nome do produto...", "loading": "Carregando produtos...", "selected": "Selecionado"}}}