import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  Box,
  InlineStack,
  Icon,
  DataTable,
  Badge,
} from "@shopify/polaris";
import { StarIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getVIPLevels, getLevelStatistics, initializeBROPointsLevels } from "app/models/VIPLevel.server";
import { useTranslation } from "app/hooks/useTranslation";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Initialize BROpoints levels if they don't exist
    await initializeBROPointsLevels(shop);

    // Get current levels and statistics
    const levels = await getVIPLevels(shop);
    const statistics = await getLevelStatistics(shop);

    const settings = {
      active: true, // For now, always active
      levels: levels.map(level => ({
        id: level.id,
        name: level.name,
        threshold: level.threshold.toString(),
        pointsMultiplier: level.pointsMultiplier.toString(),
        benefits: level.benefits,
      })),
      statistics,
      retentionPeriod: "365",
      evaluationPeriod: "90",
    };

    return json({ settings });
  } catch (error) {
    console.error("Error loading VIP program data:", error);
    return json({
      settings: {
        active: false,
        levels: [],
        statistics: {},
        retentionPeriod: "365",
        evaluationPeriod: "90"
      }
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const settings = Object.fromEntries(formData);
  
  // TODO: Implémenter la sauvegarde réelle des données
  console.log("Settings to save:", settings);
  
  return json({ success: true });
};

export default function ProgramVIP() {
  const { settings } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const { t } = useTranslation();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    submit(event.currentTarget);
  };

  const rows = settings.levels.map((level) => [
    <Text as="span" variant="bodyMd" fontWeight="bold">{level.name}</Text>,
    <Text as="span" variant="bodyMd">{level.threshold} points</Text>,
    <Text as="span" variant="bodyMd">x{level.pointsMultiplier}</Text>,
    <Text as="span" variant="bodyMd">{level.benefits.split('\n').join(', ')}</Text>,
    <Button size="slim" disabled>View</Button>, // Disabled for now as these are the fixed BROpoints levels
  ]);

  return (
    <AdminLayout title="BROpoints Level System">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Banner tone="info">
              <Text as="p" variant="bodyMd">
                The BROpoints level system automatically promotes customers based on their total points earned.
                The three levels (BRO, EhrenBRO, Bester BRO) provide increasing point multipliers to reward loyal customers.
              </Text>
            </Banner>

            <Card>
              <BlockStack gap="400">
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                    <Text as="h2" variant="headingMd">Program Status</Text>
                    <Badge tone={settings.active ? "success" : "attention"}>
                      {settings.active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">BROpoints Levels</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={StarIcon} />
                    <Text as="span" variant="bodyMd">
                      {settings.levels.length} levels configured
                    </Text>
                  </InlineStack>
                </Box>
                <DataTable
                  columnContentTypes={["text", "text", "text", "text", "text"]}
                  headings={["Level", "Threshold (points)", "Multiplier", "Benefits", "Actions"]}
                  rows={rows}
                />
                <Text as="p" variant="bodyMd" tone="subdued">
                  These are the fixed BROpoints levels. Customers are automatically promoted based on their total points earned.
                </Text>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Level Evaluation</Text>
                <Text as="p" variant="bodyMd">
                  Customer levels are evaluated automatically when points are earned or redeemed.
                  Levels are based on total points accumulated, not spending amounts.
                </Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label="Status retention period (days)"
                      type="number"
                      name="retentionPeriod"
                      value={settings.retentionPeriod}
                      autoComplete="off"
                      helpText="Duration a customer retains their level status"
                      min={0}
                      disabled
                    />
                    <TextField
                      label="Evaluation period (days)"
                      type="number"
                      name="evaluationPeriod"
                      value={settings.evaluationPeriod}
                      autoComplete="off"
                      helpText="Frequency of level status re-evaluation"
                      min={0}
                      disabled
                    />
                    <Text as="p" variant="bodyMd" tone="subdued">
                      Level evaluation settings are currently fixed for the BROpoints system.
                    </Text>
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section secondary>
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Level Statistics</Text>
                <BlockStack gap="200">
                  {settings.levels.map((level) => (
                    <Text key={level.id} as="p" variant="bodyMd">
                      {level.name} customers: <Text as="span" variant="headingSm">
                        {settings.statistics[level.name] || 0}
                      </Text>
                    </Text>
                  ))}
                </BlockStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Help</Text>
                <Text as="p" variant="bodyMd">
                  The BROpoints level system rewards your best customers with exclusive benefits.
                  Customers are automatically promoted based on their total points earned.
                </Text>
                <Text as="p" variant="bodyMd">
                  Level progression is immediate when customers reach the required point thresholds.
                  Higher levels provide better point multipliers for all earning activities.
                </Text>
                <Text as="p" variant="bodyMd">
                  <strong>BRO (0 points):</strong> Base level with 1x multiplier<br/>
                  <strong>EhrenBRO (1000 points):</strong> 1.25x point multiplier<br/>
                  <strong>Bester BRO (5000 points):</strong> 1.5x point multiplier
                </Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
} 